import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from tensorflow.keras.models import load_model
import warnings
warnings.filterwarnings('ignore')

from integrated_data_processor import IntegratedStockDataProcessor
from multimodal_model import MultiModalStockPredictor

class TradingStrategy:
    """基于深度学习预测的交易策略"""
    
    def __init__(self, 
                 model_path: str,
                 data_processor: IntegratedStockDataProcessor,
                 prediction_threshold: float = 0.02,
                 stop_loss: float = 0.05,
                 take_profit: float = 0.08,
                 max_positions: int = 10):
        """
        初始化交易策略
        
        Args:
            model_path: 训练好的模型路径
            data_processor: 数据处理器
            prediction_threshold: 预测阈值（绝对值）
            stop_loss: 止损比例
            take_profit: 止盈比例
            max_positions: 最大持仓数量
        """
        self.model_path = model_path
        self.data_processor = data_processor
        self.prediction_threshold = prediction_threshold
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        self.max_positions = max_positions
        
        # 加载模型
        self.model = None
        self.load_model()
        
        # 交易记录
        self.positions = {}  # 当前持仓
        self.trade_history = []  # 交易历史
        self.portfolio_value = []  # 组合价值历史
        
    def load_model(self):
        """加载训练好的模型"""
        try:
            # 这里需要重新创建模型架构然后加载权重
            # 或者使用自定义对象加载完整模型
            print(f"正在加载模型: {self.model_path}")
            # 注意：由于使用了自定义层，可能需要特殊处理
            # self.model = load_model(self.model_path)
            print("模型加载成功")
        except Exception as e:
            print(f"模型加载失败: {e}")
            print("请确保模型文件存在且格式正确")
    
    def predict_stock_movement(self, 
                             stock_data: pd.DataFrame, 
                             tech_data: pd.DataFrame) -> Dict[str, float]:
        """
        预测股票涨跌幅
        
        Args:
            stock_data: 股票基础数据
            tech_data: 技术强度数据
            
        Returns:
            股票代码到预测涨跌幅的映射
        """
        predictions = {}
        
        if self.model is None:
            print("模型未加载，无法进行预测")
            return predictions
        
        # 合并数据
        merged_data = self.data_processor.merge_datasets(stock_data, tech_data)
        
        # 创建技术指标
        merged_data = self.data_processor.create_price_features(merged_data)
        merged_data = self.data_processor.process_tech_strength_features(merged_data)
        
        # 准备特征
        merged_data = self.data_processor.prepare_feature_columns(merged_data)
        merged_data = merged_data.dropna()
        
        # 按股票分组预测
        for stock_code, group in merged_data.groupby('股票代码'):
            group = group.sort_values('日期')
            
            if len(group) < self.data_processor.sequence_length:
                continue
            
            # 取最近的序列数据
            recent_data = group.tail(self.data_processor.sequence_length)
            
            # 准备特征
            price_features = recent_data[self.data_processor.price_feature_columns].values
            tech_features = recent_data[self.data_processor.tech_feature_columns].values
            
            # 重塑为模型输入格式
            X_price = price_features.reshape(1, self.data_processor.sequence_length, -1)
            X_tech = tech_features.reshape(1, self.data_processor.sequence_length, -1)
            
            # 标准化
            X_price_scaled, X_tech_scaled, _ = self.data_processor.normalize_data(
                X_price, X_tech, np.array([0]), fit_scalers=False
            )
            
            # 预测
            try:
                prediction_scaled = self.model.predict([X_price_scaled, X_tech_scaled])
                prediction = self.data_processor.inverse_transform_target(prediction_scaled)[0]
                predictions[stock_code] = prediction
            except Exception as e:
                print(f"预测股票 {stock_code} 时出错: {e}")
                continue
        
        return predictions
    
    def generate_trading_signals(self, predictions: Dict[str, float]) -> Dict[str, str]:
        """
        生成交易信号
        
        Args:
            predictions: 股票预测结果
            
        Returns:
            股票代码到交易信号的映射 ('BUY', 'SELL', 'HOLD')
        """
        signals = {}
        
        for stock_code, predicted_return in predictions.items():
            if predicted_return > self.prediction_threshold:
                signals[stock_code] = 'BUY'
            elif predicted_return < -self.prediction_threshold:
                signals[stock_code] = 'SELL'
            else:
                signals[stock_code] = 'HOLD'
        
        return signals
    
    def rank_stocks_by_prediction(self, predictions: Dict[str, float], top_n: int = 20) -> List[Tuple[str, float]]:
        """
        根据预测结果排序股票
        
        Args:
            predictions: 预测结果
            top_n: 返回前N只股票
            
        Returns:
            排序后的股票列表 [(股票代码, 预测涨跌幅)]
        """
        # 按预测涨跌幅排序
        sorted_stocks = sorted(predictions.items(), key=lambda x: x[1], reverse=True)
        
        return sorted_stocks[:top_n]
    
    def calculate_position_size(self, 
                              stock_code: str, 
                              predicted_return: float, 
                              current_price: float,
                              total_capital: float) -> float:
        """
        计算仓位大小
        
        Args:
            stock_code: 股票代码
            predicted_return: 预测收益率
            current_price: 当前价格
            total_capital: 总资本
            
        Returns:
            建议仓位大小（股数）
        """
        # 基于凯利公式的仓位管理
        # 这里简化处理，实际应用中需要更复杂的风险管理
        
        # 预测胜率（简化假设）
        win_probability = 0.6 if abs(predicted_return) > self.prediction_threshold * 1.5 else 0.55
        
        # 平均盈亏比
        avg_win = self.take_profit
        avg_loss = self.stop_loss
        
        # 凯利比例
        kelly_fraction = (win_probability * avg_win - (1 - win_probability) * avg_loss) / avg_win
        kelly_fraction = max(0, min(kelly_fraction, 0.25))  # 限制最大仓位
        
        # 基于预测强度调整
        confidence_multiplier = min(abs(predicted_return) / self.prediction_threshold, 2.0)
        adjusted_fraction = kelly_fraction * confidence_multiplier * 0.5
        
        # 计算股数
        position_value = total_capital * adjusted_fraction / self.max_positions
        shares = int(position_value / current_price)
        
        return shares
    
    def execute_trading_strategy(self, 
                               current_data: pd.DataFrame,
                               current_prices: Dict[str, float],
                               total_capital: float = 1000000) -> Dict[str, any]:
        """
        执行交易策略
        
        Args:
            current_data: 当前市场数据
            current_prices: 当前股票价格
            total_capital: 总资本
            
        Returns:
            交易执行结果
        """
        results = {
            'new_positions': [],
            'closed_positions': [],
            'signals': {},
            'top_stocks': []
        }
        
        # 分离股票数据和技术强度数据
        stock_data = current_data[current_data.columns.intersection([
            '证券代码', '日期', '开盘价', '最高价', '最低价', '收盘价', 
            '前收盘价', '成交量', '成交额', '换手率', '涨跌幅'
        ])]
        
        tech_data = current_data[current_data.columns.intersection([
            '股票代码', '日期', '技术强度', '连续技术强度3天数', 
            '连续 技术强度5天数', '连续技术强度10天数', '技术指标特征', '趋势组合'
        ])]
        
        # 预测股票走势
        predictions = self.predict_stock_movement(stock_data, tech_data)
        
        if not predictions:
            print("没有生成有效预测")
            return results
        
        # 生成交易信号
        signals = self.generate_trading_signals(predictions)
        results['signals'] = signals
        
        # 获取最佳股票
        top_stocks = self.rank_stocks_by_prediction(predictions, top_n=20)
        results['top_stocks'] = top_stocks
        
        # 执行交易决策
        for stock_code, signal in signals.items():
            if stock_code not in current_prices:
                continue
                
            current_price = current_prices[stock_code]
            predicted_return = predictions[stock_code]
            
            if signal == 'BUY' and len(self.positions) < self.max_positions:
                # 买入信号
                shares = self.calculate_position_size(
                    stock_code, predicted_return, current_price, total_capital
                )
                
                if shares > 0:
                    position = {
                        'stock_code': stock_code,
                        'action': 'BUY',
                        'shares': shares,
                        'price': current_price,
                        'predicted_return': predicted_return,
                        'timestamp': pd.Timestamp.now()
                    }
                    
                    self.positions[stock_code] = position
                    results['new_positions'].append(position)
                    
            elif signal == 'SELL' and stock_code in self.positions:
                # 卖出信号
                position = self.positions[stock_code]
                
                # 计算收益
                actual_return = (current_price - position['price']) / position['price']
                
                closed_position = {
                    'stock_code': stock_code,
                    'action': 'SELL',
                    'shares': position['shares'],
                    'buy_price': position['price'],
                    'sell_price': current_price,
                    'predicted_return': position['predicted_return'],
                    'actual_return': actual_return,
                    'profit_loss': (current_price - position['price']) * position['shares'],
                    'timestamp': pd.Timestamp.now()
                }
                
                results['closed_positions'].append(closed_position)
                self.trade_history.append(closed_position)
                del self.positions[stock_code]
        
        return results
    
    def calculate_portfolio_performance(self, current_prices: Dict[str, float]) -> Dict[str, float]:
        """
        计算投资组合表现
        
        Args:
            current_prices: 当前价格
            
        Returns:
            投资组合表现指标
        """
        if not self.trade_history:
            return {'total_return': 0, 'win_rate': 0, 'avg_return': 0}
        
        # 计算已完成交易的表现
        total_profit = sum([trade['profit_loss'] for trade in self.trade_history])
        total_trades = len(self.trade_history)
        winning_trades = len([trade for trade in self.trade_history if trade['actual_return'] > 0])
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        avg_return = np.mean([trade['actual_return'] for trade in self.trade_history]) if total_trades > 0 else 0
        
        # 计算当前持仓的未实现盈亏
        unrealized_pnl = 0
        for stock_code, position in self.positions.items():
            if stock_code in current_prices:
                current_price = current_prices[stock_code]
                unrealized_pnl += (current_price - position['price']) * position['shares']
        
        performance = {
            'total_realized_profit': total_profit,
            'unrealized_pnl': unrealized_pnl,
            'total_pnl': total_profit + unrealized_pnl,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'current_positions': len(self.positions)
        }
        
        return performance
    
    def get_trading_summary(self) -> pd.DataFrame:
        """获取交易摘要"""
        if not self.trade_history:
            return pd.DataFrame()
        
        df = pd.DataFrame(self.trade_history)
        
        summary = {
            '总交易次数': len(df),
            '盈利交易次数': len(df[df['actual_return'] > 0]),
            '亏损交易次数': len(df[df['actual_return'] < 0]),
            '胜率': len(df[df['actual_return'] > 0]) / len(df),
            '平均收益率': df['actual_return'].mean(),
            '最大单次收益': df['actual_return'].max(),
            '最大单次亏损': df['actual_return'].min(),
            '总盈亏': df['profit_loss'].sum(),
            '平均预测准确度': np.mean(np.sign(df['predicted_return']) == np.sign(df['actual_return']))
        }
        
        return pd.Series(summary)
