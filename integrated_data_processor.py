import pandas as pd
import numpy as np
import os
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import glob
from typing import Tuple, List, Dict
import warnings
warnings.filterwarnings('ignore')

class IntegratedStockDataProcessor:
    """综合股票数据处理器 - 结合基础数据和技术强度数据"""
    
    def __init__(self, 
                 stock_data_path: str = "stock_data/daily", 
                 tech_strength_path: str = "tech_strength/daily",
                 sequence_length: int = 30):
        """
        初始化综合数据处理器
        
        Args:
            stock_data_path: 股票基础数据路径
            tech_strength_path: 技术强度数据路径
            sequence_length: 时间序列长度
        """
        self.stock_data_path = stock_data_path
        self.tech_strength_path = tech_strength_path
        self.sequence_length = sequence_length
        
        # 缩放器
        self.scaler_price_features = StandardScaler()
        self.scaler_tech_features = StandardScaler()
        self.scaler_target = MinMaxScaler()
        
        # 编码器
        self.label_encoders = {}
        
        # 特征列定义
        self.price_feature_columns = []
        self.tech_feature_columns = []
        self.target_column = '涨跌幅'
        
    def load_stock_data(self) -> pd.DataFrame:
        """加载股票基础数据"""
        print("正在加载股票基础数据...")
        excel_files = glob.glob(os.path.join(self.stock_data_path, "*.xlsx"))
        excel_files.sort()
        
        all_data = []
        for file in excel_files:
            try:
                df = pd.read_excel(file)
                # 统一日期格式
                df['日期'] = pd.to_datetime(df['日期'])
                all_data.append(df)
            except Exception as e:
                print(f"加载股票数据文件 {file} 时出错: {e}")
                
        if not all_data:
            raise ValueError("没有找到有效的股票数据文件")
            
        combined_data = pd.concat(all_data, ignore_index=True)
        print(f"股票基础数据加载完成: {len(combined_data)} 条记录")
        return combined_data
    
    def load_tech_strength_data(self) -> pd.DataFrame:
        """加载技术强度数据"""
        print("正在加载技术强度数据...")
        excel_files = glob.glob(os.path.join(self.tech_strength_path, "*.xlsx"))
        excel_files.sort()
        
        all_data = []
        for file in excel_files:
            try:
                df = pd.read_excel(file)
                # 统一日期格式
                df['日期'] = pd.to_datetime(df['日期'])
                all_data.append(df)
            except Exception as e:
                print(f"加载技术强度数据文件 {file} 时出错: {e}")
                
        if not all_data:
            raise ValueError("没有找到有效的技术强度数据文件")
            
        combined_data = pd.concat(all_data, ignore_index=True)
        print(f"技术强度数据加载完成: {len(combined_data)} 条记录")
        return combined_data
    
    def merge_datasets(self, stock_df: pd.DataFrame, tech_df: pd.DataFrame) -> pd.DataFrame:
        """
        合并股票基础数据和技术强度数据
        
        Args:
            stock_df: 股票基础数据
            tech_df: 技术强度数据
            
        Returns:
            合并后的数据
        """
        print("正在合并数据集...")
        
        # 统一股票代码格式
        stock_df['股票代码'] = stock_df['证券代码']
        
        # 合并数据
        merged_df = pd.merge(
            stock_df, 
            tech_df, 
            on=['股票代码', '日期'], 
            how='inner',
            suffixes=('_stock', '_tech')
        )
        
        print(f"数据合并完成: {len(merged_df)} 条记录")
        return merged_df
    
    def create_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建价格相关技术指标特征"""
        print("正在创建价格技术指标...")
        
        processed_groups = []
        
        for stock_code, group in df.groupby('股票代码'):
            group = group.sort_values('日期').copy()
            
            # 基础价格特征
            group['价格变化率'] = group['收盘价_stock'].pct_change()
            group['高低价差比'] = (group['最高价'] - group['最低价']) / group['收盘价_stock']
            group['开收价差比'] = (group['收盘价_stock'] - group['开盘价']) / group['开盘价']
            group['振幅'] = (group['最高价'] - group['最低价']) / group['前收盘价']
            
            # 移动平均线
            for window in [5, 10, 20, 30]:
                group[f'MA_{window}'] = group['收盘价_stock'].rolling(window=window).mean()
                group[f'MA_{window}_ratio'] = group['收盘价_stock'] / group[f'MA_{window}']
            
            # 指数移动平均
            group['EMA_12'] = group['收盘价_stock'].ewm(span=12).mean()
            group['EMA_26'] = group['收盘价_stock'].ewm(span=26).mean()
            
            # MACD指标
            group['MACD'] = group['EMA_12'] - group['EMA_26']
            group['MACD_signal'] = group['MACD'].ewm(span=9).mean()
            group['MACD_histogram'] = group['MACD'] - group['MACD_signal']
            
            # RSI指标
            delta = group['收盘价_stock'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            group['RSI'] = 100 - (100 / (1 + rs))
            
            # 布林带
            group['BB_middle'] = group['收盘价_stock'].rolling(window=20).mean()
            bb_std = group['收盘价_stock'].rolling(window=20).std()
            group['BB_upper'] = group['BB_middle'] + (bb_std * 2)
            group['BB_lower'] = group['BB_middle'] - (bb_std * 2)
            group['BB_position'] = (group['收盘价_stock'] - group['BB_lower']) / (group['BB_upper'] - group['BB_lower'])
            
            # 波动率指标
            group['volatility_5'] = group['价格变化率'].rolling(window=5).std()
            group['volatility_10'] = group['价格变化率'].rolling(window=10).std()
            group['volatility_20'] = group['价格变化率'].rolling(window=20).std()
            
            # 成交量指标
            group['volume_ma_5'] = group['成交量'].rolling(window=5).mean()
            group['volume_ma_10'] = group['成交量'].rolling(window=10).mean()
            group['volume_ratio_5'] = group['成交量'] / group['volume_ma_5']
            group['volume_ratio_10'] = group['成交量'] / group['volume_ma_10']
            
            # 价量关系
            group['price_volume_trend'] = (group['价格变化率'] * group['成交量']).rolling(window=5).mean()
            
            processed_groups.append(group)
        
        result_df = pd.concat(processed_groups, ignore_index=True)
        print("价格技术指标创建完成")
        return result_df
    
    def process_tech_strength_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理技术强度特征"""
        print("正在处理技术强度特征...")

        # 技术强度作为分类特征处理 (28, 42, 57, 71, 85, 100 是预定义的等级)
        # 创建技术强度等级映射
        tech_strength_mapping = {
            28: 1,   # 极弱
            42: 2,   # 弱
            57: 3,   # 中等偏弱
            71: 4,   # 中等偏强
            85: 5,   # 强
            100: 6   # 极强
        }

        # 将技术强度转换为等级
        df['技术强度_等级'] = df['技术强度'].map(tech_strength_mapping)

        # 技术强度独热编码
        df['技术强度_极弱'] = (df['技术强度'] == 28).astype(int)
        df['技术强度_弱'] = (df['技术强度'] == 42).astype(int)
        df['技术强度_中等偏弱'] = (df['技术强度'] == 57).astype(int)
        df['技术强度_中等偏强'] = (df['技术强度'] == 71).astype(int)
        df['技术强度_强'] = (df['技术强度'] == 85).astype(int)
        df['技术强度_极强'] = (df['技术强度'] == 100).astype(int)

        # 技术强度标准化 (基于等级)
        df['技术强度_normalized'] = (df['技术强度_等级'] - 1) / 5  # 0-1范围

        # 连续技术强度特征处理
        # 由于技术强度是分类值，这里改为基于等级的比率
        df['连续强度_3天_等级比'] = df['连续技术强度3天数'] / df['技术强度_等级']
        df['连续强度_5天_等级比'] = df['连续 技术强度5天数'] / df['技术强度_等级']
        df['连续强度_10天_等级比'] = df['连续技术强度10天数'] / df['技术强度_等级']

        # 连续强度相对于原始技术强度值的比率 (保留原有逻辑)
        df['连续强度_3天_ratio'] = df['连续技术强度3天数'] / df['技术强度']
        df['连续强度_5天_ratio'] = df['连续 技术强度5天数'] / df['技术强度']
        df['连续强度_10天_ratio'] = df['连续技术强度10天数'] / df['技术强度']
        
        # 技术指标特征编码
        if '技术指标特征' in df.columns:
            # 将技术指标特征转换为数值特征
            tech_feature_str = df['技术指标特征'].astype(str)
            
            # 提取各位数字作为特征
            df['tech_feature_1'] = tech_feature_str.str[0:1].astype(float)
            df['tech_feature_2'] = tech_feature_str.str[1:2].astype(float)
            df['tech_feature_3'] = tech_feature_str.str[2:3].astype(float)
            df['tech_feature_4'] = tech_feature_str.str[3:4].astype(float)
            df['tech_feature_5'] = tech_feature_str.str[4:5].astype(float)
            df['tech_feature_6'] = tech_feature_str.str[5:6].astype(float)
        
        # 趋势组合特征编码
        if '趋势组合' in df.columns:
            trend_str = df['趋势组合'].astype(str)
            
            # 提取各位数字作为特征
            df['trend_1'] = trend_str.str[0:1].astype(float)
            df['trend_2'] = trend_str.str[1:2].astype(float)
            df['trend_3'] = trend_str.str[2:3].astype(float)
            df['trend_4'] = trend_str.str[3:4].astype(float)
            df['trend_5'] = trend_str.str[4:5].astype(float)
            df['trend_6'] = trend_str.str[5:6].astype(float)
        
        # 成交量倍数特征
        df['volume_multiplier'] = df['成交量是前一日几倍']
        
        # 日内标记特征
        if '日内股票标记' in df.columns:
            df['intraday_mark'] = df['日内股票标记']
        
        print("技术强度特征处理完成")
        return df

    def create_advanced_combination_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建高级组合特征 - 深度特征挖掘
        """
        print("正在创建高级组合特征...")

        processed_groups = []

        for stock_code, group in df.groupby('股票代码'):
            group = group.sort_values('日期').copy()

            # === 价格-技术强度组合特征 ===

            # 1. 技术强度加权价格特征
            group['技术强度加权收盘价'] = group['收盘价_stock'] * group['技术强度_normalized']
            group['技术强度加权成交量'] = group['成交量'] * group['技术强度_normalized']
            group['技术强度加权换手率'] = group['换手率'] * group['技术强度_normalized']

            # 2. 技术强度与价格变化的交互特征
            group['强度_价格变化交互'] = group['技术强度_等级'] * group['价格变化率']
            group['强度_振幅交互'] = group['技术强度_等级'] * group['振幅']
            group['强度_成交量倍数交互'] = group['技术强度_等级'] * group['volume_multiplier']

            # 3. 技术强度趋势特征
            group['技术强度_3日变化'] = group['技术强度_等级'].diff(3)
            group['技术强度_5日变化'] = group['技术强度_等级'].diff(5)
            group['技术强度_趋势斜率'] = group['技术强度_等级'].rolling(window=5).apply(
                lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 5 else 0
            )

            # === 多时间窗口组合特征 ===

            # 4. 多周期技术强度一致性
            group['强度一致性_3_5'] = (group['连续强度_3天_ratio'] * group['连续强度_5天_ratio'])
            group['强度一致性_5_10'] = (group['连续强度_5天_ratio'] * group['连续强度_10天_ratio'])
            group['强度一致性_全周期'] = (group['连续强度_3天_ratio'] *
                                   group['连续强度_5天_ratio'] *
                                   group['连续强度_10天_ratio'])

            # 5. 价格动量与技术强度组合
            group['动量_强度比'] = group['MACD'] / (group['技术强度_等级'] + 1e-8)
            group['RSI_强度比'] = group['RSI'] / (group['技术强度_等级'] + 1e-8)
            group['布林位置_强度比'] = group['BB_position'] / (group['技术强度_等级'] + 1e-8)

            # === 成交量-技术强度组合特征 ===

            # 6. 成交量强度综合指标
            group['成交量强度综合'] = (group['volume_ratio_5'] * group['技术强度_normalized'] *
                                group['volume_multiplier'])
            group['价量强度协同'] = (group['price_volume_trend'] * group['技术强度_normalized'])

            # 7. 技术指标特征组合
            if all(col in group.columns for col in ['tech_feature_1', 'tech_feature_2', 'tech_feature_3']):
                group['技术指标组合_123'] = (group['tech_feature_1'] * group['tech_feature_2'] *
                                      group['tech_feature_3'])
                group['技术指标加权和'] = (group['tech_feature_1'] * 0.3 +
                                   group['tech_feature_2'] * 0.3 +
                                   group['tech_feature_3'] * 0.4)

            # 8. 趋势组合特征
            if all(col in group.columns for col in ['trend_1', 'trend_2', 'trend_3']):
                group['趋势组合_123'] = (group['trend_1'] * group['trend_2'] * group['trend_3'])
                group['趋势一致性'] = (group['trend_1'] == group['trend_2']).astype(int) * \
                                (group['trend_2'] == group['trend_3']).astype(int)

            processed_groups.append(group)

        result_df = pd.concat(processed_groups, ignore_index=True)
        print("高级组合特征创建完成")
        return result_df

    def create_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建统计学特征 - 基于历史数据的统计特征
        """
        print("正在创建统计学特征...")

        processed_groups = []

        for stock_code, group in df.groupby('股票代码'):
            group = group.sort_values('日期').copy()

            # === 滚动统计特征 ===

            # 1. 多窗口收益率统计
            for window in [3, 5, 10, 20]:
                group[f'收益率_均值_{window}日'] = group['价格变化率'].rolling(window=window).mean()
                group[f'收益率_标准差_{window}日'] = group['价格变化率'].rolling(window=window).std()
                group[f'收益率_偏度_{window}日'] = group['价格变化率'].rolling(window=window).skew()
                group[f'收益率_峰度_{window}日'] = group['价格变化率'].rolling(window=window).kurt()

                # 收益率分位数特征
                group[f'收益率_25分位_{window}日'] = group['价格变化率'].rolling(window=window).quantile(0.25)
                group[f'收益率_75分位_{window}日'] = group['价格变化率'].rolling(window=window).quantile(0.75)
                group[f'收益率_IQR_{window}日'] = (group[f'收益率_75分位_{window}日'] -
                                             group[f'收益率_25分位_{window}日'])

            # 2. 技术强度历史统计
            for window in [5, 10, 20]:
                group[f'技术强度_历史均值_{window}日'] = group['技术强度_等级'].rolling(window=window).mean()
                group[f'技术强度_历史标准差_{window}日'] = group['技术强度_等级'].rolling(window=window).std()
                group[f'技术强度_相对位置_{window}日'] = ((group['技术强度_等级'] -
                                                group[f'技术强度_历史均值_{window}日']) /
                                               (group[f'技术强度_历史标准差_{window}日'] + 1e-8))

            # === 序列模式特征 ===

            # 3. 连续上涨/下跌天数
            group['连续上涨天数'] = (group['价格变化率'] > 0).astype(int).groupby(
                (group['价格变化率'] <= 0).cumsum()).cumsum()
            group['连续下跌天数'] = (group['价格变化率'] < 0).astype(int).groupby(
                (group['价格变化率'] >= 0).cumsum()).cumsum()

            # 4. 技术强度连续性模式
            group['技术强度上升天数'] = (group['技术强度_等级'].diff() > 0).astype(int).groupby(
                (group['技术强度_等级'].diff() <= 0).cumsum()).cumsum()
            group['技术强度下降天数'] = (group['技术强度_等级'].diff() < 0).astype(int).groupby(
                (group['技术强度_等级'].diff() >= 0).cumsum()).cumsum()

            # === 相关性特征 ===

            # 5. 滚动相关性特征
            for window in [10, 20]:
                group[f'价格_成交量相关性_{window}日'] = group['价格变化率'].rolling(window=window).corr(
                    group['成交量'].pct_change())
                group[f'价格_技术强度相关性_{window}日'] = group['价格变化率'].rolling(window=window).corr(
                    group['技术强度_等级'])

            processed_groups.append(group)

        result_df = pd.concat(processed_groups, ignore_index=True)
        print("统计学特征创建完成")
        return result_df

    def create_market_microstructure_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建市场微观结构特征 - 基于交易行为的特征
        """
        print("正在创建市场微观结构特征...")

        processed_groups = []

        for stock_code, group in df.groupby('股票代码'):
            group = group.sort_values('日期').copy()

            # === 流动性特征 ===

            # 1. Amihud非流动性指标
            group['Amihud非流动性'] = abs(group['价格变化率']) / (group['成交额'] + 1e-8)
            group['Amihud非流动性_5日均值'] = group['Amihud非流动性'].rolling(window=5).mean()

            # 2. 换手率调整的价格影响
            group['价格影响指标'] = abs(group['价格变化率']) / (group['换手率'] + 1e-8)
            group['相对价格影响'] = group['价格影响指标'] / group['价格影响指标'].rolling(window=20).mean()

            # 3. 成交量价格弹性
            volume_change = group['成交量'].pct_change()
            group['成交量价格弹性'] = group['价格变化率'] / (volume_change + 1e-8)

            # === 信息不对称特征 ===

            # 4. 买卖价差代理指标 (基于高低价差)
            group['相对买卖价差'] = (group['最高价'] - group['最低价']) / group['收盘价_stock']
            group['价差波动性'] = group['相对买卖价差'].rolling(window=5).std()

            # 5. 价格发现效率
            group['价格发现延迟'] = abs(group['收盘价_stock'] - group['开盘价']) / group['开盘价']
            group['隔夜收益'] = (group['开盘价'] / group['收盘价_stock'].shift(1) - 1)
            group['日内收益'] = (group['收盘价_stock'] / group['开盘价'] - 1)
            group['隔夜日内比'] = abs(group['隔夜收益']) / (abs(group['日内收益']) + 1e-8)

            # === 技术强度与市场微观结构的交互 ===

            # 6. 技术强度调整的流动性指标
            group['强度调整非流动性'] = group['Amihud非流动性'] / (group['技术强度_normalized'] + 1e-8)
            group['强度调整换手率'] = group['换手率'] * group['技术强度_normalized']

            # 7. 信息含量指标
            group['技术强度信息含量'] = (group['技术强度_等级'] * abs(group['价格变化率']) *
                                 group['成交量'] / group['成交量'].rolling(window=20).mean())

            processed_groups.append(group)

        result_df = pd.concat(processed_groups, ignore_index=True)
        print("市场微观结构特征创建完成")
        return result_df

    def create_behavioral_finance_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建行为金融学特征 - 基于投资者行为的特征
        """
        print("正在创建行为金融学特征...")

        processed_groups = []

        for stock_code, group in df.groupby('股票代码'):
            group = group.sort_values('日期').copy()

            # === 动量与反转特征 ===

            # 1. 多期动量指标
            for period in [3, 5, 10, 20]:
                group[f'动量_{period}日'] = (group['收盘价_stock'] /
                                       group['收盘价_stock'].shift(period) - 1)
                group[f'动量强度_{period}日'] = group[f'动量_{period}日'] * group['技术强度_normalized']

            # 2. 反转指标
            group['短期反转_3日'] = -group['动量_3日']  # 短期反转
            group['中期反转_10日'] = -group['动量_10日']  # 中期反转

            # === 情绪指标 ===

            # 3. 技术强度情绪指标
            group['强度情绪指数'] = (group['技术强度_等级'] - group['技术强度_等级'].rolling(window=20).mean()) / \
                              (group['技术强度_等级'].rolling(window=20).std() + 1e-8)

            # 4. 成交量情绪
            group['成交量情绪'] = (group['成交量'] - group['成交量'].rolling(window=20).mean()) / \
                             (group['成交量'].rolling(window=20).std() + 1e-8)

            # 5. 综合情绪指标
            group['综合情绪指标'] = (group['强度情绪指数'] + group['成交量情绪']) / 2

            # === 过度反应与不足反应 ===

            # 6. 过度反应指标
            group['价格过度反应'] = abs(group['价格变化率']) / (group['volatility_5'] + 1e-8)
            group['技术强度过度反应'] = abs(group['技术强度_3日变化']) / \
                                  (group['技术强度_历史标准差_5日'] + 1e-8)

            # 7. 不足反应指标 (基于技术强度与价格变化的不一致性)
            expected_price_change = group['技术强度_normalized'] * 0.02  # 假设技术强度对应的期望收益
            group['价格不足反应'] = expected_price_change - group['价格变化率']

            processed_groups.append(group)

        result_df = pd.concat(processed_groups, ignore_index=True)
        print("行为金融学特征创建完成")
        return result_df
    
    def prepare_feature_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """准备特征列"""
        # 价格相关特征
        self.price_feature_columns = [
            '开盘价', '最高价', '最低价', '收盘价_stock', '成交量', '成交额', '换手率',
            '价格变化率', '高低价差比', '开收价差比', '振幅',
            'MA_5', 'MA_10', 'MA_20', 'MA_30',
            'MA_5_ratio', 'MA_10_ratio', 'MA_20_ratio', 'MA_30_ratio',
            'EMA_12', 'EMA_26', 'MACD', 'MACD_signal', 'MACD_histogram',
            'RSI', 'BB_middle', 'BB_upper', 'BB_lower', 'BB_position',
            'volatility_5', 'volatility_10', 'volatility_20',
            'volume_ma_5', 'volume_ma_10', 'volume_ratio_5', 'volume_ratio_10',
            'price_volume_trend'
        ]
        
        # 技术强度相关特征 (基础特征)
        basic_tech_features = [
            '技术强度', '技术强度_等级', '技术强度_normalized',
            '技术强度_极弱', '技术强度_弱', '技术强度_中等偏弱',
            '技术强度_中等偏强', '技术强度_强', '技术强度_极强',
            '连续技术强度3天数', '连续 技术强度5天数', '连续技术强度10天数',
            '连续强度_3天_ratio', '连续强度_5天_ratio', '连续强度_10天_ratio',
            '连续强度_3天_等级比', '连续强度_5天_等级比', '连续强度_10天_等级比',
            'tech_feature_1', 'tech_feature_2', 'tech_feature_3',
            'tech_feature_4', 'tech_feature_5', 'tech_feature_6',
            'trend_1', 'trend_2', 'trend_3', 'trend_4', 'trend_5', 'trend_6',
            'volume_multiplier', 'intraday_mark'
        ]

        # 高级组合特征
        combination_features = [
            '技术强度加权收盘价', '技术强度加权成交量', '技术强度加权换手率',
            '强度_价格变化交互', '强度_振幅交互', '强度_成交量倍数交互',
            '技术强度_3日变化', '技术强度_5日变化', '技术强度_趋势斜率',
            '强度一致性_3_5', '强度一致性_5_10', '强度一致性_全周期',
            '动量_强度比', 'RSI_强度比', '布林位置_强度比',
            '成交量强度综合', '价量强度协同',
            '技术指标组合_123', '技术指标加权和', '趋势组合_123', '趋势一致性'
        ]

        # 统计学特征
        statistical_features = []
        for window in [3, 5, 10, 20]:
            statistical_features.extend([
                f'收益率_均值_{window}日', f'收益率_标准差_{window}日',
                f'收益率_偏度_{window}日', f'收益率_峰度_{window}日',
                f'收益率_25分位_{window}日', f'收益率_75分位_{window}日', f'收益率_IQR_{window}日'
            ])

        for window in [5, 10, 20]:
            statistical_features.extend([
                f'技术强度_历史均值_{window}日', f'技术强度_历史标准差_{window}日',
                f'技术强度_相对位置_{window}日'
            ])

        statistical_features.extend([
            '连续上涨天数', '连续下跌天数', '技术强度上升天数', '技术强度下降天数',
            '价格_成交量相关性_10日', '价格_成交量相关性_20日',
            '价格_技术强度相关性_10日', '价格_技术强度相关性_20日'
        ])

        # 市场微观结构特征
        microstructure_features = [
            'Amihud非流动性', 'Amihud非流动性_5日均值', '价格影响指标', '相对价格影响',
            '成交量价格弹性', '相对买卖价差', '价差波动性', '价格发现延迟',
            '隔夜收益', '日内收益', '隔夜日内比', '强度调整非流动性',
            '强度调整换手率', '技术强度信息含量'
        ]

        # 行为金融学特征
        behavioral_features = []
        for period in [3, 5, 10, 20]:
            behavioral_features.extend([f'动量_{period}日', f'动量强度_{period}日'])

        behavioral_features.extend([
            '短期反转_3日', '中期反转_10日', '强度情绪指数', '成交量情绪',
            '综合情绪指标', '价格过度反应', '技术强度过度反应', '价格不足反应'
        ])

        # 合并所有技术强度相关特征
        self.tech_feature_columns = (basic_tech_features + combination_features +
                                    statistical_features + microstructure_features +
                                    behavioral_features)
        
        # 确保所有特征列都存在
        available_price_features = [col for col in self.price_feature_columns if col in df.columns]
        available_tech_features = [col for col in self.tech_feature_columns if col in df.columns]
        
        self.price_feature_columns = available_price_features
        self.tech_feature_columns = available_tech_features
        
        print(f"价格特征: {len(self.price_feature_columns)} 个")
        print(f"技术强度特征: {len(self.tech_feature_columns)} 个")
        
        # 选择需要的列
        required_columns = (self.price_feature_columns + self.tech_feature_columns + 
                          [self.target_column, '股票代码', '日期'])
        
        return df[required_columns]

    def create_sequences(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        创建多模态时间序列数据

        Args:
            df: 特征DataFrame

        Returns:
            X_price: 价格特征序列 (samples, sequence_length, price_features)
            X_tech: 技术强度特征序列 (samples, sequence_length, tech_features)
            y: 目标值 (samples,)
        """
        print("正在创建多模态时间序列...")

        X_price, X_tech, y = [], [], []

        # 按股票代码分组创建序列
        for stock_code, group in df.groupby('股票代码'):
            group = group.sort_values('日期')

            if len(group) < self.sequence_length + 1:
                continue

            price_features = group[self.price_feature_columns].values
            tech_features = group[self.tech_feature_columns].values
            targets = group[self.target_column].values

            for i in range(len(price_features) - self.sequence_length):
                X_price.append(price_features[i:i + self.sequence_length])
                X_tech.append(tech_features[i:i + self.sequence_length])
                y.append(targets[i + self.sequence_length])

        X_price = np.array(X_price)
        X_tech = np.array(X_tech)
        y = np.array(y)

        print(f"创建了 {len(X_price)} 个序列样本")
        print(f"价格特征形状: {X_price.shape}")
        print(f"技术强度特征形状: {X_tech.shape}")
        print(f"目标形状: {y.shape}")

        return X_price, X_tech, y

    def normalize_data(self, X_price: np.ndarray, X_tech: np.ndarray, y: np.ndarray,
                      fit_scalers: bool = True) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        标准化多模态数据

        Args:
            X_price: 价格特征数据
            X_tech: 技术强度特征数据
            y: 目标数据
            fit_scalers: 是否拟合缩放器

        Returns:
            标准化后的X_price, X_tech, y
        """
        print("正在标准化多模态数据...")

        # 标准化价格特征
        original_price_shape = X_price.shape
        X_price_reshaped = X_price.reshape(-1, X_price.shape[-1])

        if fit_scalers:
            X_price_scaled = self.scaler_price_features.fit_transform(X_price_reshaped)
        else:
            X_price_scaled = self.scaler_price_features.transform(X_price_reshaped)

        X_price_scaled = X_price_scaled.reshape(original_price_shape)

        # 标准化技术强度特征
        original_tech_shape = X_tech.shape
        X_tech_reshaped = X_tech.reshape(-1, X_tech.shape[-1])

        if fit_scalers:
            X_tech_scaled = self.scaler_tech_features.fit_transform(X_tech_reshaped)
        else:
            X_tech_scaled = self.scaler_tech_features.transform(X_tech_reshaped)

        X_tech_scaled = X_tech_scaled.reshape(original_tech_shape)

        # 标准化目标值
        if fit_scalers:
            y_scaled = self.scaler_target.fit_transform(y.reshape(-1, 1)).flatten()
        else:
            y_scaled = self.scaler_target.transform(y.reshape(-1, 1)).flatten()

        return X_price_scaled, X_tech_scaled, y_scaled

    def process_integrated_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """
        完整的综合数据处理流程

        Returns:
            X_price_train, X_price_test, X_tech_train, X_tech_test, y_train, y_test
        """
        # 加载数据
        stock_df = self.load_stock_data()
        tech_df = self.load_tech_strength_data()

        # 合并数据
        merged_df = self.merge_datasets(stock_df, tech_df)

        # 创建价格技术指标
        merged_df = self.create_price_features(merged_df)

        # 处理技术强度特征
        merged_df = self.process_tech_strength_features(merged_df)

        # 创建高级组合特征
        merged_df = self.create_advanced_combination_features(merged_df)

        # 创建统计学特征
        merged_df = self.create_statistical_features(merged_df)

        # 创建市场微观结构特征
        merged_df = self.create_market_microstructure_features(merged_df)

        # 创建行为金融学特征
        merged_df = self.create_behavioral_finance_features(merged_df)

        # 准备特征列
        merged_df = self.prepare_feature_columns(merged_df)

        # 删除包含NaN的行
        merged_df = merged_df.dropna()
        print(f"清理后数据量: {len(merged_df)} 条记录")

        # 创建序列
        X_price, X_tech, y = self.create_sequences(merged_df)

        # 分割数据
        indices = np.arange(len(X_price))
        train_indices, test_indices = train_test_split(
            indices, test_size=0.2, random_state=42, shuffle=False
        )

        X_price_train, X_price_test = X_price[train_indices], X_price[test_indices]
        X_tech_train, X_tech_test = X_tech[train_indices], X_tech[test_indices]
        y_train, y_test = y[train_indices], y[test_indices]

        # 标准化
        X_price_train, X_tech_train, y_train = self.normalize_data(
            X_price_train, X_tech_train, y_train, fit_scalers=True
        )
        X_price_test, X_tech_test, y_test = self.normalize_data(
            X_price_test, X_tech_test, y_test, fit_scalers=False
        )

        print("综合数据处理完成!")
        print(f"训练集: X_price {X_price_train.shape}, X_tech {X_tech_train.shape}, y {y_train.shape}")
        print(f"测试集: X_price {X_price_test.shape}, X_tech {X_tech_test.shape}, y {y_test.shape}")

        return X_price_train, X_price_test, X_tech_train, X_tech_test, y_train, y_test

    def inverse_transform_target(self, y_scaled: np.ndarray) -> np.ndarray:
        """
        反标准化目标值

        Args:
            y_scaled: 标准化后的目标值

        Returns:
            原始尺度的目标值
        """
        return self.scaler_target.inverse_transform(y_scaled.reshape(-1, 1)).flatten()
