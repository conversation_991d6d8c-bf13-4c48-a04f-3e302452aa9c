#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高胜率组合专项验证系统
只验证我们发现的高胜率组合，分别统计各组合的实际表现

作者: AI Assistant
日期: 2025-07-01
"""

import os
import sys
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
from sklearn.preprocessing import LabelEncoder

class HighWinRateValidation:
    """高胜率组合专项验证"""
    
    def __init__(self, prediction_dir="预测文件资料", stock_data_dir="stock_data/daily"):
        self.prediction_dir = prediction_dir
        self.stock_data_dir = stock_data_dir
        self.label_encoders = {}
        
        # 只验证高胜率组合 (胜率≥70%)
        self.high_win_rate_rules = [
            # 超高胜率组合 (≥95%)
            {
                'name': '三特征趋势组合',
                'condition': lambda row: (row.get('连续技术强度5天数_三分', 0) == 3.0 and
                                        row.get('趋势组合_编码', 0) == 2 and
                                        row.get('涨跌幅_分段', 0) == 5.0),
                'expected_win_rate': 0.988,
                'expected_return': 0.1811,
                'confidence': 'PERFECT',
                'description': '连续技术强度5天高段 + 趋势2 + 高涨跌幅',
                'sample_count': 86
            },
            
            # 优秀胜率组合 (85-95%)
            {
                'name': '技术强度100',
                'condition': lambda row: row.get('技术强度', 0) == 100,
                'expected_win_rate': 0.890,
                'expected_return': 0.0343,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100',
                'sample_count': 30635
            },
            {
                'name': '技术强度100+成交量3.5倍',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 3.5),
                'expected_win_rate': 0.891,
                'expected_return': 0.0661,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 + 成交量3.5倍',
                'sample_count': 2156
            },
            {
                'name': '技术强度100+成交量3.0倍',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 3.0),
                'expected_win_rate': 0.912,
                'expected_return': 0.0543,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 + 成交量3.0倍',
                'sample_count': 1035
            },
            {
                'name': '技术强度100+成交量2.5倍',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 2.5),
                'expected_win_rate': 0.924,
                'expected_return': 0.0504,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 + 成交量2.5倍',
                'sample_count': 1575
            },
            {
                'name': '技术强度100+成交量2.0倍',
                'condition': lambda row: (row.get('技术强度', 0) == 100 and 
                                        row.get('成交量是前一日几倍', 0) == 2.0),
                'expected_win_rate': 0.921,
                'expected_return': 0.0430,
                'confidence': 'EXCELLENT',
                'description': '技术强度=100 + 成交量2.0倍',
                'sample_count': 3086
            },
            
            # 很好胜率组合 (80-85%)
            {
                'name': '技术强度85',
                'condition': lambda row: row.get('技术强度', 0) == 85,
                'expected_win_rate': 0.813,
                'expected_return': 0.0203,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=85',
                'sample_count': 12244
            },
            {
                'name': '技术强度85+成交量3.5倍',
                'condition': lambda row: (row.get('技术强度', 0) == 85 and 
                                        row.get('成交量是前一日几倍', 0) == 3.5),
                'expected_win_rate': 0.849,
                'expected_return': 0.0463,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=85 + 成交量3.5倍',
                'sample_count': 1948
            },
            {
                'name': '技术强度85+成交量3.0倍',
                'condition': lambda row: (row.get('技术强度', 0) == 85 and 
                                        row.get('成交量是前一日几倍', 0) == 3.0),
                'expected_win_rate': 0.876,
                'expected_return': 0.0398,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=85 + 成交量3.0倍',
                'sample_count': 1082
            },
            {
                'name': '技术强度85+成交量2.5倍',
                'condition': lambda row: (row.get('技术强度', 0) == 85 and 
                                        row.get('成交量是前一日几倍', 0) == 2.5),
                'expected_win_rate': 0.895,
                'expected_return': 0.0376,
                'confidence': 'VERY_GOOD',
                'description': '技术强度=85 + 成交量2.5倍',
                'sample_count': 2044
            },
            
            # 良好胜率组合 (75-80%)
            {
                'name': '成交量3.5倍',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) == 3.5,
                'expected_win_rate': 0.786,
                'expected_return': 0.0402,
                'confidence': 'GOOD',
                'description': '成交量3.5倍',
                'sample_count': 6522
            },
            {
                'name': '成交量3.0倍',
                'condition': lambda row: row.get('成交量是前一日几倍', 0) == 3.0,
                'expected_win_rate': 0.808,
                'expected_return': 0.0340,
                'confidence': 'GOOD',
                'description': '成交量3.0倍',
                'sample_count': 4016
            },
            
            # 中等胜率组合 (70-75%)
            {
                'name': '技术强度71',
                'condition': lambda row: row.get('技术强度', 0) == 71,
                'expected_win_rate': 0.744,
                'expected_return': 0.0074,
                'confidence': 'MODERATE',
                'description': '技术强度=71',
                'sample_count': 24489
            }
        ]
    
    def create_features(self, df):
        """创建特征"""
        # 处理分类特征
        categorical_features = ['技术指标特征', '趋势组合', '日内股票标记']
        for feature in categorical_features:
            if feature in df.columns:
                df[feature] = df[feature].fillna('Unknown')
                if feature not in self.label_encoders:
                    self.label_encoders[feature] = LabelEncoder()
                    df[f'{feature}_编码'] = self.label_encoders[feature].fit_transform(df[feature].astype(str))
        
        # 连续技术强度分段
        if '连续技术强度5天数' in df.columns:
            min_val, max_val = 28, 500
            range_size = max_val - min_val
            cut1 = min_val + range_size / 3
            cut2 = min_val + 2 * range_size / 3
            df['连续技术强度5天数_三分'] = pd.cut(
                df['连续技术强度5天数'], 
                bins=[min_val-1, cut1, cut2, max_val+1], 
                labels=[1, 2, 3]
            ).astype(float)
        
        # 涨跌幅分段
        if '涨跌幅' in df.columns:
            df['涨跌幅_分段'] = pd.cut(df['涨跌幅'], bins=5, labels=[1, 2, 3, 4, 5]).astype(float)
        
        return df
    
    def classify_stock_by_rules(self, row):
        """按高胜率规则分类股票"""
        # 按优先级检查规则 (从高胜率到低胜率)
        for rule in self.high_win_rate_rules:
            try:
                if rule['condition'](row):
                    return rule
            except:
                continue
        
        # 不属于任何高胜率组合
        return None
    
    def load_and_process_data(self, date_str):
        """加载并处理数据"""
        print("=" * 80)
        print(f"🔍 高胜率组合专项验证: {date_str}")
        print("=" * 80)
        
        # 1. 加载预测数据
        filename = f"tech_strength_strong_{date_str}_smart.xlsx"
        file_path = os.path.join(self.prediction_dir, filename)
        
        if not os.path.exists(file_path):
            print(f"❌ 预测文件不存在: {filename}")
            return None
        
        try:
            df = pd.read_excel(file_path)
            print(f"📂 加载预测数据: {filename} ({len(df)} 只股票)")
        except Exception as e:
            print(f"❌ 加载预测文件失败: {e}")
            return None
        
        # 2. 创建特征
        df = self.create_features(df)
        
        # 3. 按高胜率规则分类
        print(f"🎯 按高胜率组合分类股票...")
        
        rule_classifications = []
        for _, row in df.iterrows():
            rule = self.classify_stock_by_rules(row)
            rule_classifications.append(rule)
        
        # 4. 统计各组合的股票数量
        rule_counts = {}
        for rule in rule_classifications:
            if rule is not None:
                rule_name = rule['name']
                if rule_name not in rule_counts:
                    rule_counts[rule_name] = 0
                rule_counts[rule_name] += 1
        
        print(f"\n📊 高胜率组合分布:")
        total_high_win_rate = sum(rule_counts.values())
        for rule_name, count in sorted(rule_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = count / len(df) * 100
            print(f"  {rule_name}: {count} 只 ({percentage:.1f}%)")
        
        print(f"\n  高胜率组合总计: {total_high_win_rate} 只 ({total_high_win_rate/len(df)*100:.1f}%)")
        print(f"  非高胜率组合: {len(df) - total_high_win_rate} 只 ({(len(df) - total_high_win_rate)/len(df)*100:.1f}%)")
        
        # 5. 加载实际数据
        prediction_date = datetime.strptime(date_str, '%Y-%m-%d')
        next_date = prediction_date + timedelta(days=1)
        
        # 跳过周末
        while next_date.weekday() >= 5:
            next_date += timedelta(days=1)
        
        actual_data = self.load_actual_data(prediction_date, next_date)
        if actual_data is None:
            return None
        
        # 6. 计算实际收益
        actual_returns = self.calculate_actual_returns(actual_data, prediction_date, next_date)
        if len(actual_returns) == 0:
            return None
        
        # 7. 为每只股票添加规则信息
        df['匹配规则'] = [rule['name'] if rule else '非高胜率组合' for rule in rule_classifications]
        df['预期胜率'] = [rule['expected_win_rate'] if rule else 0.536 for rule in rule_classifications]
        df['预期收益率'] = [rule['expected_return'] if rule else 0.0022 for rule in rule_classifications]
        df['置信等级'] = [rule['confidence'] if rule else 'LOW' for rule in rule_classifications]
        
        # 8. 合并实际结果
        merged_df = pd.merge(df, actual_returns, on='股票代码', how='inner')
        
        if len(merged_df) == 0:
            print(f"❌ 预测数据与实际数据无法匹配")
            return None
        
        print(f"🔗 成功匹配: {len(merged_df)} 只股票")
        
        return merged_df, prediction_date, next_date
    
    def load_actual_data(self, prediction_date, next_date):
        """加载实际交易数据"""
        prediction_file = f"stock_data_{prediction_date.strftime('%Y%m%d')}.xlsx"
        next_file = f"stock_data_{next_date.strftime('%Y%m%d')}.xlsx"
        
        prediction_path = os.path.join(self.stock_data_dir, prediction_file)
        next_path = os.path.join(self.stock_data_dir, next_file)
        
        actual_data = []
        
        # 加载预测日数据
        if os.path.exists(prediction_path):
            try:
                df_pred = pd.read_excel(prediction_path)
                df_pred['日期'] = prediction_date
                actual_data.append(df_pred)
            except Exception as e:
                print(f"❌ 加载 {prediction_file} 失败: {e}")
        
        # 加载次日数据
        if os.path.exists(next_path):
            try:
                df_next = pd.read_excel(next_path)
                df_next['日期'] = next_date
                actual_data.append(df_next)
            except Exception as e:
                print(f"❌ 加载 {next_file} 失败: {e}")
        
        if actual_data:
            combined_df = pd.concat(actual_data, ignore_index=True)
            return combined_df
        
        return None
    
    def calculate_actual_returns(self, actual_data, prediction_date, next_date):
        """计算实际收益率"""
        actual_returns = []
        
        for stock_code, group in actual_data.groupby('证券代码'):
            group = group.sort_values('日期')
            
            # 查找预测日和次日数据
            pred_day = group[group['日期'] == prediction_date]
            next_day = group[group['日期'] == next_date]
            
            if len(pred_day) > 0 and len(next_day) > 0:
                buy_price = pred_day.iloc[0]['开盘价']  # 预测日开盘价买入
                sell_price = next_day.iloc[0]['开盘价']  # 次日开盘价卖出
                
                if buy_price > 0 and sell_price > 0:
                    actual_return = (sell_price - buy_price) / buy_price
                    
                    actual_returns.append({
                        '股票代码': stock_code,
                        '买入价格': buy_price,
                        '卖出价格': sell_price,
                        '实际收益率': actual_return,
                        '实际上涨': actual_return > 0
                    })
        
        return pd.DataFrame(actual_returns)
    
    def analyze_high_win_rate_performance(self, merged_df, prediction_date, next_date):
        """分析高胜率组合表现"""
        print(f"\n📊 高胜率组合实际表现分析:")
        print(f"  预测日期: {prediction_date.strftime('%Y-%m-%d')}")
        print(f"  验证日期: {next_date.strftime('%Y-%m-%d')}")
        
        # 只分析高胜率组合
        high_win_rate_df = merged_df[merged_df['匹配规则'] != '非高胜率组合']
        
        if len(high_win_rate_df) == 0:
            print(f"❌ 没有匹配到高胜率组合")
            return []
        
        print(f"\n🎯 高胜率组合总体表现:")
        total_high_win_rate = len(high_win_rate_df)
        correct_high_win_rate = (high_win_rate_df['实际上涨'] == True).sum()
        high_win_rate_accuracy = correct_high_win_rate / total_high_win_rate
        avg_actual_return = high_win_rate_df['实际收益率'].mean()
        
        print(f"  高胜率组合股票数: {total_high_win_rate}")
        print(f"  实际上涨股票数: {correct_high_win_rate}")
        print(f"  高胜率组合实际胜率: {high_win_rate_accuracy:.3f} ({high_win_rate_accuracy*100:.1f}%)")
        print(f"  高胜率组合平均收益: {avg_actual_return:.4f} ({avg_actual_return*100:.2f}%)")
        
        # 按具体规则分析
        print(f"\n📋 各高胜率组合详细表现:")
        rule_performance = []
        
        for rule_name in high_win_rate_df['匹配规则'].unique():
            subset = high_win_rate_df[high_win_rate_df['匹配规则'] == rule_name]
            
            if len(subset) > 0:
                actual_win_rate = subset['实际上涨'].mean()
                expected_win_rate = subset['预期胜率'].iloc[0]
                actual_return = subset['实际收益率'].mean()
                expected_return = subset['预期收益率'].iloc[0]
                count = len(subset)
                
                win_rate_diff = actual_win_rate - expected_win_rate
                return_diff = actual_return - expected_return
                
                print(f"\n  🎯 {rule_name}: {count}只")
                print(f"      预期胜率: {expected_win_rate:.3f} ({expected_win_rate*100:.1f}%)")
                print(f"      实际胜率: {actual_win_rate:.3f} ({actual_win_rate*100:.1f}%)")
                print(f"      胜率偏差: {win_rate_diff:+.3f} ({win_rate_diff*100:+.1f}%)")
                print(f"      预期收益: {expected_return:.4f} ({expected_return*100:.2f}%)")
                print(f"      实际收益: {actual_return:.4f} ({actual_return*100:.2f}%)")
                print(f"      收益偏差: {return_diff:+.4f} ({return_diff*100:+.2f}%)")
                
                # 评估表现
                if actual_win_rate >= expected_win_rate * 0.95:  # 实际胜率≥预期胜率的95%
                    performance = "✅ 优秀"
                elif actual_win_rate >= expected_win_rate * 0.85:  # 实际胜率≥预期胜率的85%
                    performance = "👍 良好"
                elif actual_win_rate >= expected_win_rate * 0.75:  # 实际胜率≥预期胜率的75%
                    performance = "⚠️ 一般"
                else:
                    performance = "❌ 较差"
                
                print(f"      表现评级: {performance}")
                
                rule_performance.append({
                    'rule_name': rule_name,
                    'count': count,
                    'expected_win_rate': expected_win_rate,
                    'actual_win_rate': actual_win_rate,
                    'win_rate_diff': win_rate_diff,
                    'expected_return': expected_return,
                    'actual_return': actual_return,
                    'return_diff': return_diff,
                    'performance': performance
                })
        
        # 显示最佳表现的组合
        print(f"\n🏆 表现最佳的高胜率组合:")
        best_performers = [r for r in rule_performance if r['actual_win_rate'] >= 0.80]
        best_performers.sort(key=lambda x: x['actual_win_rate'], reverse=True)
        
        for i, rule in enumerate(best_performers[:5], 1):
            print(f"  {i}. {rule['rule_name']}: {rule['actual_win_rate']:.1%}实际胜率 ({rule['count']}只)")
        
        return rule_performance, high_win_rate_accuracy
    
    def save_high_win_rate_results(self, merged_df, rule_performance, overall_accuracy, prediction_date, output_path):
        """保存高胜率组合验证结果"""
        # 只保存高胜率组合的结果
        high_win_rate_df = merged_df[merged_df['匹配规则'] != '非高胜率组合']
        
        if len(high_win_rate_df) == 0:
            print(f"❌ 没有高胜率组合数据可保存")
            return
        
        # 准备详细结果
        detailed_results = high_win_rate_df[[
            '股票代码', '股票名称',
            '匹配规则', '预期胜率', '预期收益率',
            '实际收益率', '实际上涨',
            '买入价格', '卖出价格'
        ]].copy()
        
        # 添加分析列
        detailed_results['胜率达标'] = detailed_results['实际上涨']
        detailed_results['收益率偏差'] = detailed_results['实际收益率'] - detailed_results['预期收益率']
        
        # 按预期胜率排序
        detailed_results = detailed_results.sort_values('预期胜率', ascending=False)
        
        # 保存到Excel
        with pd.ExcelWriter(output_path) as writer:
            # 详细结果
            detailed_results.to_excel(writer, sheet_name='高胜率组合验证', index=False)
            
            # 规则表现摘要
            if rule_performance:
                summary_data = []
                for rule in rule_performance:
                    summary_data.append({
                        '组合名称': rule['rule_name'],
                        '股票数量': rule['count'],
                        '预期胜率': f"{rule['expected_win_rate']*100:.1f}%",
                        '实际胜率': f"{rule['actual_win_rate']*100:.1f}%",
                        '胜率偏差': f"{rule['win_rate_diff']*100:+.1f}%",
                        '预期收益率': f"{rule['expected_return']*100:.2f}%",
                        '实际收益率': f"{rule['actual_return']*100:.2f}%",
                        '收益偏差': f"{rule['return_diff']*100:+.2f}%",
                        '表现评级': rule['performance']
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='组合表现摘要', index=False)
        
        print(f"💾 高胜率组合验证结果已保存到: {output_path}")

def main():
    """主函数"""
    validator = HighWinRateValidation()
    
    # 要验证的日期
    validation_dates = ['2025-06-30', '2025-07-01', '2025-07-02']
    
    all_results = []
    
    for date_str in validation_dates:
        try:
            print(f"\n" + "="*80)
            
            # 处理数据
            result = validator.load_and_process_data(date_str)
            
            if result is not None:
                merged_df, prediction_date, next_date = result
                
                # 分析高胜率组合表现
                rule_performance, overall_accuracy = validator.analyze_high_win_rate_performance(merged_df, prediction_date, next_date)
                
                # 保存结果
                output_path = f"高胜率组合验证_{date_str.replace('-', '')}.xlsx"
                validator.save_high_win_rate_results(merged_df, rule_performance, overall_accuracy, prediction_date, output_path)
                
                all_results.append({
                    'date': date_str,
                    'high_win_rate_accuracy': overall_accuracy,
                    'rule_performance': rule_performance
                })
                
                print(f"✅ {date_str} 高胜率组合验证完成")
            else:
                print(f"❌ {date_str} 验证失败")
                
        except Exception as e:
            print(f"❌ 验证 {date_str} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # 总结所有结果
    if all_results:
        print(f"\n" + "="*80)
        print(f"📊 高胜率组合验证总结")
        print(f"="*80)
        
        avg_accuracy = sum(r['high_win_rate_accuracy'] for r in all_results) / len(all_results)
        
        print(f"验证日期数: {len(all_results)}")
        print(f"高胜率组合平均实际胜率: {avg_accuracy:.3f} ({avg_accuracy*100:.1f}%)")
        
        print(f"\n各日期高胜率组合表现:")
        for result in all_results:
            print(f"  {result['date']}: {result['high_win_rate_accuracy']:.3f} ({result['high_win_rate_accuracy']*100:.1f}%)")
    
    print(f"\n🎉 高胜率组合专项验证完成！")

if __name__ == "__main__":
    main()
