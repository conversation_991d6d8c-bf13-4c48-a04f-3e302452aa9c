#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票预测系统主程序
基于CNN+LSTM+Attention的多模态深度学习股票预测系统

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import argparse
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from integrated_data_processor import IntegratedStockDataProcessor
from multimodal_model import MultiModalStockPredictor
from training_system import StockPredictionTrainingSystem
from trading_strategy import TradingStrategy
from visualization_backtest import VisualizationBacktest

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票预测系统')
    parser.add_argument('--mode', type=str, choices=['train', 'predict', 'backtest'], 
                       default='train', help='运行模式')
    parser.add_argument('--stock_data_path', type=str, default='stock_data/daily',
                       help='股票数据路径')
    parser.add_argument('--tech_strength_path', type=str, default='tech_strength/daily',
                       help='技术强度数据路径')
    parser.add_argument('--model_path', type=str, default='models/best_model.h5',
                       help='模型文件路径')
    parser.add_argument('--sequence_length', type=int, default=30,
                       help='时间序列长度')
    parser.add_argument('--epochs', type=int, default=100,
                       help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=32,
                       help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                       help='学习率')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🚀 股票预测系统 - CNN+LSTM+Attention")
    print("=" * 60)
    print(f"运行模式: {args.mode}")
    print(f"股票数据路径: {args.stock_data_path}")
    print(f"技术强度数据路径: {args.tech_strength_path}")
    print("=" * 60)
    
    if args.mode == 'train':
        train_model(args)
    elif args.mode == 'predict':
        predict_stocks(args)
    elif args.mode == 'backtest':
        run_backtest(args)

def train_model(args):
    """训练模型"""
    print("🔥 开始模型训练...")
    
    # 创建训练系统
    training_system = StockPredictionTrainingSystem(
        stock_data_path=args.stock_data_path,
        tech_strength_path=args.tech_strength_path,
        sequence_length=args.sequence_length
    )
    
    # 运行完整训练流程
    try:
        results = training_system.run_complete_training(
            cnn_filters=[64, 128, 256],
            lstm_units=[128, 64],
            attention_heads=8,
            dropout_rate=0.3,
            learning_rate=args.learning_rate,
            epochs=args.epochs,
            batch_size=args.batch_size,
            validation_split=0.2
        )
        
        print("✅ 模型训练完成!")
        print(f"最终测试集方向准确率: {results['direction_accuracy_original']:.4f}")
        print(f"最终测试集MAE: {results['mae_original']:.6f}")
        print(f"最终测试集R²: {results['r2_original']:.6f}")
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        sys.exit(1)

def predict_stocks(args):
    """预测股票"""
    print("🔮 开始股票预测...")
    
    if not os.path.exists(args.model_path):
        print(f"❌ 模型文件不存在: {args.model_path}")
        print("请先运行训练模式生成模型")
        sys.exit(1)
    
    try:
        # 创建数据处理器
        data_processor = IntegratedStockDataProcessor(
            stock_data_path=args.stock_data_path,
            tech_strength_path=args.tech_strength_path,
            sequence_length=args.sequence_length
        )
        
        # 创建交易策略
        strategy = TradingStrategy(
            model_path=args.model_path,
            data_processor=data_processor,
            prediction_threshold=0.02,
            stop_loss=0.05,
            take_profit=0.08
        )
        
        # 加载最新数据进行预测
        print("📊 加载最新数据...")
        stock_data = data_processor.load_stock_data()
        tech_data = data_processor.load_tech_strength_data()
        
        # 获取最新日期的数据
        latest_date = max(stock_data['日期'].max(), tech_data['日期'].max())
        current_stock_data = stock_data[stock_data['日期'] == latest_date]
        current_tech_data = tech_data[tech_data['日期'] == latest_date]
        
        print(f"预测日期: {latest_date}")
        print(f"股票数据: {len(current_stock_data)} 条")
        print(f"技术强度数据: {len(current_tech_data)} 条")
        
        # 进行预测
        predictions = strategy.predict_stock_movement(current_stock_data, current_tech_data)
        
        if predictions:
            # 生成交易信号
            signals = strategy.generate_trading_signals(predictions)
            
            # 获取推荐股票
            top_stocks = strategy.rank_stocks_by_prediction(predictions, top_n=20)
            
            # 显示结果
            print("\n📈 预测结果:")
            print(f"总预测股票数: {len(predictions)}")
            
            print("\n🔝 TOP 10 推荐股票:")
            for i, (stock_code, pred_return) in enumerate(top_stocks[:10], 1):
                signal = signals.get(stock_code, 'HOLD')
                print(f"{i:2d}. {stock_code}: {pred_return:+.4f} ({signal})")
            
            # 统计交易信号
            signal_counts = {}
            for signal in signals.values():
                signal_counts[signal] = signal_counts.get(signal, 0) + 1
            
            print(f"\n📊 交易信号统计:")
            for signal, count in signal_counts.items():
                print(f"{signal}: {count} 只股票")
            
            # 保存预测结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"predictions_{timestamp}.json"
            
            prediction_results = {
                'timestamp': timestamp,
                'prediction_date': str(latest_date),
                'total_stocks': len(predictions),
                'predictions': predictions,
                'signals': signals,
                'top_stocks': top_stocks[:20],
                'signal_counts': signal_counts
            }
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(prediction_results, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 预测结果已保存到: {results_file}")
            
        else:
            print("❌ 没有生成有效的预测结果")
            
    except Exception as e:
        print(f"❌ 预测过程中出现错误: {e}")
        sys.exit(1)

def run_backtest(args):
    """运行回测"""
    print("📊 开始回测分析...")
    
    try:
        # 创建可视化回测模块
        viz_backtest = VisualizationBacktest()
        
        # 这里需要实现完整的回测逻辑
        # 由于篇幅限制，这里只提供框架
        print("回测功能正在开发中...")
        print("请使用训练模式训练模型，然后使用预测模式进行预测")
        
    except Exception as e:
        print(f"❌ 回测过程中出现错误: {e}")
        sys.exit(1)

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'tensorflow', 'pandas', 'numpy', 'scikit-learn', 
        'matplotlib', 'seaborn', 'plotly'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        sys.exit(1)

def print_system_info():
    """打印系统信息"""
    print("\n📋 系统信息:")
    print(f"Python版本: {sys.version}")
    
    try:
        import tensorflow as tf
        print(f"TensorFlow版本: {tf.__version__}")
    except ImportError:
        print("TensorFlow: 未安装")
    
    try:
        import pandas as pd
        print(f"Pandas版本: {pd.__version__}")
    except ImportError:
        print("Pandas: 未安装")
    
    try:
        import numpy as np
        print(f"NumPy版本: {np.__version__}")
    except ImportError:
        print("NumPy: 未安装")

if __name__ == "__main__":
    # 检查依赖
    check_dependencies()
    
    # 打印系统信息
    print_system_info()
    
    # 运行主程序
    main()
    
    print("\n🎉 程序执行完成!")
