import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import (
    Input, Dense, LSTM, Conv1D, MaxPooling1D, Dropout, BatchNormalization,
    Attention, MultiHeadAttention, LayerNormalization, Concatenate, 
    GlobalAveragePooling1D, Reshape, Add
)
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
import numpy as np
from typing import Tuple

class MultiModalStockPredictor:
    """多模态股票预测模型 - CNN+LSTM+Attention"""
    
    def __init__(self, 
                 sequence_length: int = 30,
                 price_features: int = 35,
                 tech_features: int = 20,
                 cnn_filters: list = [64, 128, 256],
                 lstm_units: list = [128, 64],
                 attention_heads: int = 8,
                 dropout_rate: float = 0.3):
        """
        初始化多模态模型
        
        Args:
            sequence_length: 时间序列长度
            price_features: 价格特征数量
            tech_features: 技术强度特征数量
            cnn_filters: CNN滤波器数量列表
            lstm_units: LSTM单元数量列表
            attention_heads: 注意力头数量
            dropout_rate: Dropout比率
        """
        self.sequence_length = sequence_length
        self.price_features = price_features
        self.tech_features = tech_features
        self.cnn_filters = cnn_filters
        self.lstm_units = lstm_units
        self.attention_heads = attention_heads
        self.dropout_rate = dropout_rate
        
        self.model = None
        self.history = None
    
    def build_price_branch(self, price_input):
        """构建价格特征分支 - CNN+LSTM"""
        # CNN层提取局部特征
        x = price_input
        
        for i, filters in enumerate(self.cnn_filters):
            x = Conv1D(filters=filters, kernel_size=3, activation='relu', 
                      padding='same', name=f'price_conv1d_{i+1}')(x)
            x = BatchNormalization(name=f'price_bn_conv_{i+1}')(x)
            x = MaxPooling1D(pool_size=2, name=f'price_maxpool_{i+1}')(x)
            x = Dropout(self.dropout_rate, name=f'price_dropout_conv_{i+1}')(x)
        
        # LSTM层捕获时序依赖
        for i, units in enumerate(self.lstm_units):
            return_sequences = i < len(self.lstm_units) - 1
            x = LSTM(units=units, return_sequences=return_sequences, 
                    name=f'price_lstm_{i+1}')(x)
            x = BatchNormalization(name=f'price_bn_lstm_{i+1}')(x)
            x = Dropout(self.dropout_rate, name=f'price_dropout_lstm_{i+1}')(x)
        
        return x
    
    def build_tech_branch(self, tech_input):
        """构建技术强度特征分支 - LSTM+Attention"""
        # LSTM层处理技术强度时序
        x = tech_input
        
        for i, units in enumerate(self.lstm_units):
            return_sequences = True  # 技术强度分支保持序列用于注意力机制
            x = LSTM(units=units, return_sequences=return_sequences, 
                    name=f'tech_lstm_{i+1}')(x)
            x = BatchNormalization(name=f'tech_bn_lstm_{i+1}')(x)
            x = Dropout(self.dropout_rate, name=f'tech_dropout_lstm_{i+1}')(x)
        
        # 多头自注意力机制
        attention_output = MultiHeadAttention(
            num_heads=self.attention_heads,
            key_dim=self.lstm_units[-1] // self.attention_heads,
            name='tech_multihead_attention'
        )(x, x)
        
        # 残差连接和层归一化
        x = Add(name='tech_residual')([x, attention_output])
        x = LayerNormalization(name='tech_layer_norm')(x)
        
        # 全局平均池化
        x = GlobalAveragePooling1D(name='tech_global_avg_pool')(x)
        
        return x
    
    def build_fusion_module(self, price_features, tech_features):
        """构建特征融合模块"""
        # 特征融合
        fused_features = Concatenate(name='feature_fusion')([price_features, tech_features])
        
        # 融合后的全连接层
        x = Dense(256, activation='relu', name='fusion_dense_1')(fused_features)
        x = BatchNormalization(name='fusion_bn_1')(x)
        x = Dropout(self.dropout_rate, name='fusion_dropout_1')(x)
        
        x = Dense(128, activation='relu', name='fusion_dense_2')(x)
        x = BatchNormalization(name='fusion_bn_2')(x)
        x = Dropout(self.dropout_rate, name='fusion_dropout_2')(x)
        
        x = Dense(64, activation='relu', name='fusion_dense_3')(x)
        x = BatchNormalization(name='fusion_bn_3')(x)
        x = Dropout(self.dropout_rate, name='fusion_dropout_3')(x)
        
        return x
    
    def build_model(self):
        """构建完整的多模态模型"""
        # 输入层
        price_input = Input(shape=(self.sequence_length, self.price_features), 
                           name='price_input')
        tech_input = Input(shape=(self.sequence_length, self.tech_features), 
                          name='tech_input')
        
        # 价格特征分支
        price_branch = self.build_price_branch(price_input)
        
        # 技术强度特征分支
        tech_branch = self.build_tech_branch(tech_input)
        
        # 特征融合
        fused_features = self.build_fusion_module(price_branch, tech_branch)
        
        # 输出层
        output = Dense(1, activation='linear', name='prediction_output')(fused_features)
        
        # 创建模型
        self.model = Model(inputs=[price_input, tech_input], outputs=output, 
                          name='MultiModalStockPredictor')
        
        return self.model
    
    def compile_model(self, learning_rate: float = 0.001):
        """编译模型"""
        if self.model is None:
            self.build_model()
        
        optimizer = Adam(learning_rate=learning_rate)
        
        self.model.compile(
            optimizer=optimizer,
            loss='mse',
            metrics=['mae', 'mape']
        )
        
        return self.model
    
    def get_callbacks(self, model_save_path: str = 'best_model.h5'):
        """获取训练回调函数"""
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-7,
                verbose=1
            ),
            ModelCheckpoint(
                filepath=model_save_path,
                monitor='val_loss',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            )
        ]
        
        return callbacks
    
    def train(self, 
              X_price_train: np.ndarray, 
              X_tech_train: np.ndarray, 
              y_train: np.ndarray,
              X_price_val: np.ndarray = None,
              X_tech_val: np.ndarray = None,
              y_val: np.ndarray = None,
              epochs: int = 100,
              batch_size: int = 32,
              validation_split: float = 0.2,
              model_save_path: str = 'best_model.h5'):
        """
        训练模型
        
        Args:
            X_price_train: 训练集价格特征
            X_tech_train: 训练集技术强度特征
            y_train: 训练集目标值
            X_price_val: 验证集价格特征
            X_tech_val: 验证集技术强度特征
            y_val: 验证集目标值
            epochs: 训练轮数
            batch_size: 批次大小
            validation_split: 验证集比例
            model_save_path: 模型保存路径
        """
        if self.model is None:
            self.compile_model()
        
        # 准备验证数据
        if X_price_val is not None and X_tech_val is not None and y_val is not None:
            validation_data = ([X_price_val, X_tech_val], y_val)
            validation_split = None
        else:
            validation_data = None
        
        # 获取回调函数
        callbacks = self.get_callbacks(model_save_path)
        
        # 训练模型
        print("开始训练多模态股票预测模型...")
        self.history = self.model.fit(
            [X_price_train, X_tech_train], y_train,
            epochs=epochs,
            batch_size=batch_size,
            validation_split=validation_split,
            validation_data=validation_data,
            callbacks=callbacks,
            verbose=1
        )
        
        print("模型训练完成!")
        return self.history
    
    def predict(self, X_price: np.ndarray, X_tech: np.ndarray) -> np.ndarray:
        """预测"""
        if self.model is None:
            raise ValueError("模型尚未构建或训练")
        
        predictions = self.model.predict([X_price, X_tech])
        return predictions.flatten()
    
    def evaluate(self, X_price: np.ndarray, X_tech: np.ndarray, y_true: np.ndarray) -> dict:
        """评估模型"""
        if self.model is None:
            raise ValueError("模型尚未构建或训练")
        
        # 模型评估
        loss, mae, mape = self.model.evaluate([X_price, X_tech], y_true, verbose=0)
        
        # 预测
        y_pred = self.predict(X_price, X_tech)
        
        # 计算额外指标
        mse = np.mean((y_true - y_pred) ** 2)
        rmse = np.sqrt(mse)
        
        # 方向准确率
        direction_accuracy = np.mean(np.sign(y_true) == np.sign(y_pred))
        
        metrics = {
            'loss': loss,
            'mae': mae,
            'mape': mape,
            'mse': mse,
            'rmse': rmse,
            'direction_accuracy': direction_accuracy
        }
        
        return metrics
    
    def get_model_summary(self):
        """获取模型摘要"""
        if self.model is None:
            self.build_model()
        
        return self.model.summary()
